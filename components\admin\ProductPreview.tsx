"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Eye,
  Package,
  Star,
  ShoppingCart,
  Plus,
  Minus,
  Image as ImageIcon,
  Upload
} from "lucide-react"
import { ProductTemplate, DynamicField } from "@/lib/types"
import { ImageFieldComponent } from "./ImageFieldComponent"
import { InteractiveProductForm } from "./InteractiveProductForm"

interface ProductPreviewProps {
  template: ProductTemplate
  mode: "desktop" | "mobile"
  interactive?: boolean
}

export function ProductPreview({ template, mode, interactive = false }: ProductPreviewProps) {
  // Render field based on its type
  const renderField = (field: DynamicField) => {
    if (!field.visible) return null

    const baseClasses = "bg-slate-700/50 border-slate-600 text-white"
    
    switch (field.type) {
      case "heading":
        const HeadingTag = `h${field.level || 2}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag 
            key={field.id}
            className={`font-bold text-white mb-4 ${
              field.level === 1 ? "text-2xl lg:text-3xl" :
              field.level === 2 ? "text-xl lg:text-2xl" :
              field.level === 3 ? "text-lg lg:text-xl" :
              "text-base lg:text-lg"
            } ${
              field.alignment === "center" ? "text-center" :
              field.alignment === "left" ? "text-left" :
              "text-right"
            }`}
          >
            {field.label}
          </HeadingTag>
        )

      case "divider":
        return (
          <div key={field.id} className="my-6">
            <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent" />
          </div>
        )

      case "text":
      case "email":
      case "number":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Input
              type={field.type === "email" ? "email" : field.type === "number" ? "number" : "text"}
              placeholder={field.placeholder}
              className={baseClasses}
              disabled
            />
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "textarea":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Textarea
              placeholder={field.placeholder}
              className={baseClasses}
              disabled
            />
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "select":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Select disabled>
              <SelectTrigger className={baseClasses}>
                <SelectValue placeholder={field.placeholder || "اختر..."} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.id} value={option.value}>
                    {option.label}
                    {option.price && (
                      <span className="text-green-400 mr-2">+{option.price} ج.س.</span>
                    )}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "package_selector":
        return (
          <div key={field.id} className="space-y-4">
            <Label className="text-slate-300 text-lg font-semibold">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <div className={`grid gap-3 ${
              mode === "mobile" ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-3"
            }`}>
              {field.packages?.map((pkg) => (
                <Card key={pkg.id} className="bg-slate-700/50 border-slate-600 hover:bg-slate-600/50 transition-colors cursor-pointer">
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      {pkg.popular && (
                        <Badge className="bg-yellow-500/20 text-yellow-400 text-xs mb-2">
                          الأكثر شعبية
                        </Badge>
                      )}
                      <h4 className="font-bold text-white">{pkg.name}</h4>
                      <p className="text-slate-300">{pkg.amount}</p>
                      <div className="space-y-1">
                        <p className="text-xl font-bold text-green-400">{pkg.price} ج.س.</p>
                        {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                          <p className="text-sm text-slate-400 line-through">
                            {pkg.originalPrice} ج.س.
                          </p>
                        )}
                      </div>
                      {pkg.description && (
                        <p className="text-xs text-slate-400">{pkg.description}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "quantity_selector":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" disabled className="border-slate-600">
                <Minus className="h-4 w-4" />
              </Button>
              <Input
                type="number"
                value="1"
                className={`${baseClasses} text-center w-20`}
                disabled
              />
              <Button variant="outline" size="sm" disabled className="border-slate-600">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "radio":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <label key={option.id} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name={field.name}
                    disabled
                    className="text-blue-500 border-slate-600 bg-slate-700"
                  />
                  <span className="text-white">{option.label}</span>
                  {option.price && option.price > 0 && (
                    <span className="text-green-400 text-sm">+{option.price} ج.س.</span>
                  )}
                </label>
              ))}
            </div>
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "checkbox":
        return (
          <div key={field.id} className="flex items-center space-x-2 space-x-reverse">
            <input
              type="checkbox"
              disabled
              className="rounded border-slate-600 bg-slate-700"
            />
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
          </div>
        )

      case "image":
        return (
          <ImageFieldComponent
            key={field.id}
            field={field}
            disabled={true}
            preview={true}
          />
        )

      case "price_display":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">{field.label}</Label>
            <div className="text-2xl font-bold text-green-400">
              150 ج.س.
            </div>
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      default:
        return (
          <div key={field.id} className="p-4 bg-slate-700/30 border border-slate-600 rounded-lg">
            <p className="text-slate-400">نوع حقل غير مدعوم: {field.type}</p>
          </div>
        )
    }
  }

  // If interactive mode, use the interactive form
  if (interactive) {
    return (
      <div className="space-y-6">
        {/* Preview Header */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Eye className="h-5 w-5" />
              معاينة تفاعلية - {mode === "mobile" ? "الجوال" : "سطح المكتب"}
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Interactive Product Form */}
        <div className={`${mode === "mobile" ? "max-w-sm mx-auto" : "max-w-4xl mx-auto"}`}>
          <InteractiveProductForm
            template={template}
            currency="SDG"
            showPricing={true}
            onSubmit={(formData) => {
              console.log("Form submitted:", formData)
            }}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Preview Header */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Eye className="h-5 w-5" />
            معاينة المنتج - {mode === "mobile" ? "الجوال" : "سطح المكتب"}
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Product Preview */}
      <div className={`${mode === "mobile" ? "max-w-sm mx-auto" : "max-w-4xl mx-auto"}`}>
        <Card 
          className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm"
          style={{ 
            backgroundColor: template.layout.theme.backgroundColor,
            color: template.layout.theme.textColor 
          }}
        >
          <CardHeader>
            <div className="text-center space-y-4">
              <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                {template.name}
              </h1>
              {template.description && (
                <p className="text-slate-300">{template.description}</p>
              )}
              <Badge variant="secondary">{template.category}</Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Render all fields */}
            {template.fields
              .sort((a, b) => a.order - b.order)
              .map(renderField)}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-slate-700">
              <Button 
                className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                disabled
              >
                <ShoppingCart className="h-4 w-4 ml-2" />
                إضافة إلى السلة
              </Button>
              <Button 
                variant="outline" 
                className="border-slate-600 text-slate-300"
                disabled
              >
                <Star className="h-4 w-4 ml-2" />
                إضافة للمفضلة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Preview Info */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-slate-400">
            <span>عدد الحقول: {template.fields.length}</span>
            <span>آخر تحديث: {template.updatedAt.toLocaleDateString('ar-SA')}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
