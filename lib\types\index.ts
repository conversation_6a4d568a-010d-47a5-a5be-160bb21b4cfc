// Common types used across the application

export interface GameCard {
  title: string
  subtitle: string
  gradient: string
  icon: string
}

export interface MenuItem {
  icon: React.ReactNode
  label: string
  href: string
}

export interface Slide {
  id: number
  title: string
  subtitle: string
  buttonText: string
  gradient: string
  image: string
}

export interface Feature {
  icon: string
  title: string
  desc: string
}

export interface Stat {
  number: string
  label: string
}

export interface NavigationItem {
  id: string
  icon: React.ReactNode
  label: string
  center?: boolean
}

// ===== DYNAMIC CMS SYSTEM TYPES =====

// Core field types supported by the CMS
export type FieldType =
  | "text"
  | "email"
  | "password"
  | "phone"
  | "number"
  | "textarea"
  | "select"
  | "package_selector"
  | "grouped_packages"
  | "account_type_selector"
  | "credentials_group"
  | "image"
  | "checkbox"
  | "radio"
  | "quantity_selector"
  | "price_display"
  | "divider"
  | "heading"

// Base interface for all field types
export interface BaseField {
  id: string
  type: FieldType
  label: string
  name: string
  required: boolean
  order: number
  visible: boolean
  description?: string
  placeholder?: string
  validation?: FieldValidation
}

// Validation rules for fields
export interface FieldValidation {
  min?: number
  max?: number
  pattern?: string
  customMessage?: string
}

// Text input field
export interface TextField extends BaseField {
  type: "text" | "email" | "textarea"
  maxLength?: number
  minLength?: number
}

// Number input field
export interface NumberField extends BaseField {
  type: "number" | "quantity_selector"
  min?: number
  max?: number
  step?: number
  defaultValue?: number
}

// Select dropdown field
export interface SelectField extends BaseField {
  type: "select" | "radio"
  options: SelectOption[]
  multiple?: boolean
}

export interface SelectOption {
  id: string
  label: string
  value: string
  price?: number
  description?: string
  image?: string
}

// Package selector (for gaming products)
export interface PackageSelectorField extends BaseField {
  type: "package_selector"
  packages: Package[]
  allowMultiple?: boolean
  displayStyle: "grid" | "list" | "cards"
}

export interface Package {
  id: string
  name: string
  description?: string
  amount: string
  price: number
  originalPrice?: number
  discount?: number
  popular?: boolean
  image?: string
  features?: string[]
}

// Image field
export interface ImageField extends BaseField {
  type: "image"
  maxSize?: number // in MB
  allowedTypes?: string[]
  multiple?: boolean
}

// Checkbox field
export interface CheckboxField extends BaseField {
  type: "checkbox"
  defaultChecked?: boolean
}

// Price display field (read-only)
export interface PriceDisplayField extends BaseField {
  type: "price_display"
  currency: string
  showOriginalPrice?: boolean
  showDiscount?: boolean
}

// Divider field (visual separator)
export interface DividerField extends BaseField {
  type: "divider"
  style: "line" | "space" | "gradient"
  height?: number
}

// Heading field (section titles)
export interface HeadingField extends BaseField {
  type: "heading"
  level: 1 | 2 | 3 | 4 | 5 | 6
  alignment: "left" | "center" | "right"
  color?: string
}

// Password field
export interface PasswordField extends BaseField {
  type: "password"
  minLength?: number
  maxLength?: number
  showStrengthIndicator?: boolean
}

// Phone field
export interface PhoneField extends BaseField {
  type: "phone"
  countryCode?: string
  format?: string
  validation?: {
    pattern?: string
    message?: string
  }
}

// Grouped packages field (for categorized packages)
export interface GroupedPackagesField extends BaseField {
  type: "grouped_packages"
  groups: PackageGroup[]
  allowMultiple?: boolean
  displayStyle: "tabs" | "accordion" | "sections"
}

export interface PackageGroup {
  id: string
  name: string
  description?: string
  packages: Package[]
  icon?: string
}

// Account type selector field
export interface AccountTypeSelectorField extends BaseField {
  type: "account_type_selector"
  accountTypes: AccountType[]
  defaultType?: string
}

export interface AccountType {
  id: string
  name: string
  description?: string
  icon?: string
  fields?: string[] // IDs of fields that should be shown for this account type
}

// Credentials group field (for grouping login-related fields)
export interface CredentialsGroupField extends BaseField {
  type: "credentials_group"
  fields: DynamicField[]
  title?: string
  description?: string
  collapsible?: boolean
}

// Union type for all field types
export type DynamicField =
  | TextField
  | PasswordField
  | PhoneField
  | NumberField
  | SelectField
  | PackageSelectorField
  | GroupedPackagesField
  | AccountTypeSelectorField
  | CredentialsGroupField
  | ImageField
  | CheckboxField
  | PriceDisplayField
  | DividerField
  | HeadingField

// Product template system
export interface ProductTemplate {
  id: string
  name: string
  description?: string
  category: string
  fields: DynamicField[]
  layout: ProductLayout
  createdAt: Date
  updatedAt: Date
  isDefault?: boolean
  previewImage?: string
}

// Layout configuration for products
export interface ProductLayout {
  sections: LayoutSection[]
  mobileLayout?: LayoutSection[]
  theme: ProductTheme
}

export interface LayoutSection {
  id: string
  name: string
  fieldIds: string[]
  columns: number
  spacing: "tight" | "normal" | "loose"
  background?: string
  padding?: string
  order: number
}

export interface ProductTheme {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  textColor: string
  borderRadius: "none" | "small" | "medium" | "large"
  shadows: boolean
  animations: boolean
}

// Dynamic product instance (created from template)
export interface DynamicProduct {
  id: string
  templateId: string
  slug: string
  title: string
  description?: string
  category: string
  status: "draft" | "published" | "archived"
  fields: DynamicField[]
  layout: ProductLayout
  seo: ProductSEO
  pricing: ProductPricing
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

export interface ProductSEO {
  metaTitle?: string
  metaDescription?: string
  keywords?: string[]
  ogImage?: string
}

export interface ProductPricing {
  basePrice?: number
  currency: string
  priceOverride?: boolean // If true, package/option prices override base price
  taxIncluded: boolean
  showPriceRange: boolean
}

// Form submission data
export interface ProductFormData {
  [fieldName: string]: any
}

// CMS Admin interfaces
export interface CMSSettings {
  allowedFileTypes: string[]
  maxFileSize: number
  defaultCurrency: string
  enablePreview: boolean
  autoSave: boolean
  theme: AdminTheme
}

export interface AdminTheme {
  darkMode: boolean
  primaryColor: string
  sidebarCollapsed: boolean
}

// Field creation wizard
export interface FieldWizardStep {
  id: string
  title: string
  description: string
  component: React.ComponentType<any>
  validation?: (data: any) => boolean
}

// Drag and drop interfaces
export interface DragDropField {
  id: string
  type: FieldType
  label: string
  isDragging?: boolean
  isOver?: boolean
}

export interface DropZone {
  id: string
  accepts: FieldType[]
  onDrop: (field: DragDropField) => void
  children: React.ReactNode
}

// Wallet and Currency Types
export type Currency = "SDG" | "EGP"

export interface CurrencyInfo {
  code: Currency
  name: string
  symbol: string
  arabicName: string
}

export interface WalletBalance {
  currency: Currency
  amount: number
  lastUpdated: Date
}

export interface Transaction {
  id: string
  type: "deposit" | "withdrawal" | "purchase"
  amount: number
  currency: Currency
  description: string
  date: Date
  status: "completed" | "pending" | "failed"
  reference?: string
}

export interface WalletData {
  balances: WalletBalance[]
  selectedCurrency: Currency
  totalPurchases: number
  transactions: Transaction[]
}

// Checkout System Types
export interface BankAccount {
  id: string
  name: string
  accountNumber: string
  logoUrl?: string
  isActive: boolean
}

export interface RechargeOption {
  id: string
  amount: number
  currency: Currency
  isActive: boolean
}

export interface CheckoutUserDetails {
  firstName: string
  lastName: string
  phone: string
  email: string
}

export interface CheckoutData {
  step: 1 | 2 | 3
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails | null
  selectedBank: BankAccount | null
  referenceNumber: string
  receiptFile: File | null
  receiptPreview: string | null
}

export interface CheckoutOrder {
  id: string
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails
  selectedBank: BankAccount
  referenceNumber: string
  receiptFileName?: string
  status: "pending" | "completed" | "failed"
  createdAt: Date
}

// ===== PRODUCT ORDER SYSTEM TYPES =====

export type OrderStatus = "pending" | "processing" | "completed" | "failed" | "cancelled"
export type ProcessingType = "instant" | "manual"
export type OrderType = "product_order" | "recharge_order"

export interface PriceModifier {
  fieldName: string
  fieldLabel: string
  modifier: number
  type: "add" | "multiply" | "override"
  source: "option" | "package" | "quantity"
}

export interface OrderPricing {
  basePrice: number
  modifiers: PriceModifier[]
  quantity: number
  subtotal: number
  totalPrice: number
  currency: Currency
}

export interface OrderEvent {
  id: string
  type: "created" | "status_change" | "admin_note" | "user_action" | "system_action"
  description: string
  details?: Record<string, any>
  createdAt: Date
  createdBy?: string // user ID or "system"
}

export interface ProductOrder {
  id: string
  type: OrderType
  templateId: string
  templateName: string
  templateCategory: string
  productData: Record<string, any> // All field values from form submission
  pricing: OrderPricing
  userDetails: CheckoutUserDetails
  status: OrderStatus
  processingType: ProcessingType
  adminNotes?: string
  internalNotes?: string // Private admin notes
  timeline: OrderEvent[]
  attachments?: OrderAttachment[]
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  // Supabase integration fields
  userId?: string // Will be populated from auth
  assignedAdminId?: string
  priority?: "low" | "normal" | "high" | "urgent"
}

export interface OrderAttachment {
  id: string
  orderId: string
  fieldName: string
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  uploadedAt: Date
}

// Admin order management interfaces
export interface OrderFilters {
  status?: OrderStatus[]
  type?: OrderType[]
  processingType?: ProcessingType[]
  dateRange?: {
    start: Date
    end: Date
  }
  templateId?: string
  assignedAdmin?: string
  priority?: string[]
  search?: string
}

export interface OrderListItem {
  id: string
  templateName: string
  customerName: string
  customerEmail: string
  totalPrice: number
  currency: Currency
  status: OrderStatus
  processingType: ProcessingType
  createdAt: Date
  priority?: string
}

export interface AdminOrderStats {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
  cancelled: number
  todayOrders: number
  avgProcessingTime: number // in hours
  revenue: Record<Currency, number>
}

export interface CheckoutConfig {
  bankAccounts: BankAccount[]
  rechargeOptions: RechargeOption[]
  notes: string[]
  lastUpdated: Date
}

// Contact Page Management Types
export interface ContactInfo {
  id: string
  whatsapp: string
  email: string
  workingHours: string
  location: string
  createdAt: string
  updatedAt: string
}

export interface ContactFormField {
  id: string
  name: string
  label: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select'
  required: boolean
  placeholder: string
  options?: string[] // For select fields
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
  }
  order: number
  isActive: boolean
}

export interface AboutUsSection {
  id: string
  title: string
  subtitle: string
  vision: {
    title: string
    description: string
    icon: string
  }
  mission: {
    title: string
    description: string
    icon: string
  }
  values: {
    title: string
    items: string[]
    icon: string
  }
  team: {
    title: string
    description: string
    icon: string
  }
  createdAt: string
  updatedAt: string
}

export interface CompanyStats {
  id: string
  customers: {
    value: string
    label: string
    color: string
  }
  successRate: {
    value: string
    label: string
    color: string
  }
  averageTime: {
    value: string
    label: string
    color: string
  }
  support: {
    value: string
    label: string
    color: string
  }
  createdAt: string
  updatedAt: string
}

export interface FAQ {
  id: string
  question: string
  answer: string
  order: number
  isActive: boolean
  category?: string
  createdAt: string
  updatedAt: string
}

export interface TrustIndicator {
  id: string
  title: string
  description: string
  icon: string
  color: string
  order: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ContactPageHeader {
  id: string
  title: string
  description: string
  backgroundImage?: string
  createdAt: string
  updatedAt: string
}

export interface ContactPageContent {
  id: string
  header: ContactPageHeader
  contactInfo: ContactInfo
  formFields: ContactFormField[]
  aboutUs: AboutUsSection
  stats: CompanyStats
  faqs: FAQ[]
  trustIndicators: TrustIndicator[]
  createdAt: string
  updatedAt: string
}

// Contact Form Submission Types
export interface ContactFormSubmission {
  id: string
  formData: Record<string, string>
  submittedAt: string
  status: 'new' | 'read' | 'responded' | 'closed'
  response?: string
  respondedAt?: string
  respondedBy?: string
  ipAddress?: string
  userAgent?: string
}

// Contact Admin Action Types
export type ContactAdminAction = 
  | 'edit_header'
  | 'edit_contact_info'
  | 'manage_form_fields'
  | 'edit_about_us'
  | 'edit_stats'
  | 'manage_faqs'
  | 'manage_trust_indicators'
  | 'view_submissions'
  | 'export_data'
