"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { 
  X,
  User,
  Mail,
  Phone,
  Package,
  Calendar,
  Clock,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle,
  <PERSON>rash2,
  <PERSON>,
  <PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  Flag,
  MessageSquare,
  Eye,
  EyeOff,
  Copy,
  Download
} from "lucide-react"
import { 
  ProductOrder, 
  OrderStatus, 
  DynamicField,
  OrderEvent 
} from "@/lib/types"
import { 
  updateProductOrderStatus, 
  deleteProductOrder 
} from "@/lib/utils/orderStorage"
import { 
  updateOrderStatus,
  formatFieldDataForDisplay 
} from "@/lib/utils/orderUtils"
import { formatCurrency } from "@/lib/data/currencies"

interface AdminOrderDetailsProps {
  order: ProductOrder
  onClose: () => void
  onOrderUpdate: () => void
  userRole?: "admin" | "moderator" | "viewer"
}

export function AdminOrderDetails({ 
  order, 
  onClose, 
  onOrderUpdate, 
  userRole = "admin" 
}: AdminOrderDetailsProps) {
  // State management
  const [activeTab, setActiveTab] = useState<"details" | "timeline" | "actions">("details")
  const [isEditing, setIsEditing] = useState(false)
  const [adminNotes, setAdminNotes] = useState(order.adminNotes || "")
  const [internalNotes, setInternalNotes] = useState(order.internalNotes || "")
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order.status)
  const [selectedPriority, setSelectedPriority] = useState(order.priority || "normal")
  const [assignedAdmin, setAssignedAdmin] = useState(order.assignedAdminId || "")
  const [showSensitiveData, setShowSensitiveData] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Status management
  const handleStatusUpdate = async (newStatus: OrderStatus, notes?: string) => {
    if (userRole === "viewer") return

    setIsLoading(true)
    try {
      // ## Supabase Integration: Replace with supabase update
      updateProductOrderStatus(order.id, newStatus, notes, "admin_current")
      onOrderUpdate()
      
      // Show success message
      alert(`تم تحديث حالة الطلب إلى "${getStatusLabel(newStatus)}" بنجاح`)
    } catch (error) {
      console.error("Error updating order status:", error)
      alert("حدث خطأ أثناء تحديث حالة الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  // Delete order
  const handleDeleteOrder = async () => {
    if (userRole !== "admin") return

    setIsLoading(true)
    try {
      // ## Supabase Integration: Replace with supabase delete
      deleteProductOrder(order.id)
      onOrderUpdate()
      onClose()
      
      alert("تم حذف الطلب بنجاح")
    } catch (error) {
      console.error("Error deleting order:", error)
      alert("حدث خطأ أثناء حذف الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  // Save notes and other updates
  const handleSaveUpdates = async () => {
    if (userRole === "viewer") return

    setIsLoading(true)
    try {
      // ## Supabase Integration: Replace with supabase update
      // For now, we'll update localStorage
      const orders = JSON.parse(localStorage.getItem("product_orders") || "[]")
      const orderIndex = orders.findIndex((o: ProductOrder) => o.id === order.id)
      
      if (orderIndex !== -1) {
        orders[orderIndex] = {
          ...orders[orderIndex],
          adminNotes,
          internalNotes,
          priority: selectedPriority,
          assignedAdminId: assignedAdmin,
          updatedAt: new Date()
        }
        localStorage.setItem("product_orders", JSON.stringify(orders))
      }

      onOrderUpdate()
      setIsEditing(false)
      alert("تم حفظ التحديثات بنجاح")
    } catch (error) {
      console.error("Error saving updates:", error)
      alert("حدث خطأ أثناء حفظ التحديثات")
    } finally {
      setIsLoading(false)
    }
  }

  // Helper functions
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "processing":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "failed":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "cancelled":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />
      case "processing":
        return <AlertCircle className="h-4 w-4" />
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "failed":
      case "cancelled":
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusLabel = (status: OrderStatus) => {
    const labels: Record<OrderStatus, string> = {
      pending: "في الانتظار",
      processing: "قيد المعالجة",
      completed: "مكتمل",
      failed: "فشل",
      cancelled: "ملغي"
    }
    return labels[status]
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "high":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30"
      case "normal":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "low":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const getPriorityLabel = (priority: string) => {
    const labels: Record<string, string> = {
      urgent: "عاجل",
      high: "عالي",
      normal: "عادي",
      low: "منخفض"
    }
    return labels[priority] || "عادي"
  }

  // Format field values for display
  const formatFieldValue = (fieldName: string, value: any, maskSensitive: boolean = true): string => {
    if (!value) return "-"

    // Handle sensitive fields
    if (maskSensitive && (fieldName.includes("password") || fieldName.includes("credentials"))) {
      return "••••••••"
    }

    // Handle different value types
    if (typeof value === "object") {
      if (value.name && value.price) {
        // Package selector format
        return `${value.name} (${formatCurrency(value.price, order.pricing.currency)})`
      } else if (fieldName.includes("credentials")) {
        // Credentials group format
        return Object.entries(value)
          .map(([key, val]) => `${key}: ${maskSensitive && key.includes("password") ? "••••••••" : val}`)
          .join(", ")
      } else {
        // Generic object format
        return JSON.stringify(value, null, 2)
      }
    }

    // Handle boolean values
    if (typeof value === "boolean") {
      return value ? "✓ نعم" : "✗ لا"
    }

    // Handle file uploads
    if (fieldName.includes("image") || fieldName.includes("file")) {
      return "📎 ملف مرفق"
    }

    // Default string format
    return String(value)
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="bg-slate-800 border-slate-700 max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <CardHeader className="border-b border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white flex items-center gap-2">
                <Package className="h-5 w-5" />
                تفاصيل الطلب: {order.id}
              </CardTitle>
              <div className="flex items-center gap-2 mt-2">
                <Badge className={getStatusColor(order.status)}>
                  {getStatusIcon(order.status)}
                  <span className="mr-1">{getStatusLabel(order.status)}</span>
                </Badge>
                <Badge className={getPriorityColor(order.priority || "normal")}>
                  <Flag className="h-3 w-3 mr-1" />
                  {getPriorityLabel(order.priority || "normal")}
                </Badge>
                <Badge variant="outline" className="text-slate-300">
                  {order.processingType === "instant" ? "معالجة فورية" : "معالجة يدوية"}
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {userRole !== "viewer" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                  className="border-slate-600 text-slate-300"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  {isEditing ? "إلغاء التعديل" : "تعديل"}
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className="p-0 overflow-y-auto max-h-[calc(90vh-120px)]">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-3 bg-slate-800/50 m-4 mb-0">
              <TabsTrigger value="details" className="data-[state=active]:bg-slate-700">
                تفاصيل الطلب
              </TabsTrigger>
              <TabsTrigger value="timeline" className="data-[state=active]:bg-slate-700">
                سجل الأحداث
              </TabsTrigger>
              <TabsTrigger value="actions" className="data-[state=active]:bg-slate-700">
                إجراءات الإدارة
              </TabsTrigger>
            </TabsList>

            {/* Order Details Tab */}
            <TabsContent value="details" className="p-4 space-y-6">
              {/* Customer Information */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <User className="h-5 w-5" />
                    معلومات العميل
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label className="text-slate-400">الاسم الكامل</Label>
                    <p className="text-white font-medium">{order.userDetails.fullName}</p>
                  </div>
                  <div>
                    <Label className="text-slate-400">البريد الإلكتروني</Label>
                    <div className="flex items-center gap-2">
                      <p className="text-white font-medium">{order.userDetails.email}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(order.userDetails.email)}
                        className="h-6 w-6 p-0 text-slate-400 hover:text-white"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label className="text-slate-400">رقم الهاتف</Label>
                    <div className="flex items-center gap-2">
                      <p className="text-white font-medium">{order.userDetails.phone}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(order.userDetails.phone)}
                        className="h-6 w-6 p-0 text-slate-400 hover:text-white"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Product Information */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    معلومات المنتج
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-slate-400">اسم المنتج</Label>
                    <p className="text-white font-medium">{order.templateName}</p>
                  </div>
                  <div>
                    <Label className="text-slate-400">الفئة</Label>
                    <p className="text-white font-medium">{order.templateCategory}</p>
                  </div>
                  <div>
                    <Label className="text-slate-400">تاريخ الإنشاء</Label>
                    <p className="text-white font-medium">
                      {new Date(order.createdAt).toLocaleString('ar-SA')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-slate-400">آخر تحديث</Label>
                    <p className="text-white font-medium">
                      {new Date(order.updatedAt).toLocaleString('ar-SA')}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Pricing Information */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    تفاصيل السعر
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {order.pricing.basePrice > 0 && (
                    <div className="flex justify-between">
                      <span className="text-slate-400">السعر الأساسي:</span>
                      <span className="text-white">
                        {formatCurrency(order.pricing.basePrice, order.pricing.currency)}
                      </span>
                    </div>
                  )}
                  
                  {order.pricing.modifiers.map((modifier, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-slate-400">{modifier.fieldLabel}:</span>
                      <span className="text-white">
                        {modifier.type === "add" ? "+" : ""}
                        {formatCurrency(modifier.modifier, order.pricing.currency)}
                      </span>
                    </div>
                  ))}
                  
                  {order.pricing.quantity > 1 && (
                    <div className="flex justify-between">
                      <span className="text-slate-400">الكمية:</span>
                      <span className="text-white">× {order.pricing.quantity}</span>
                    </div>
                  )}
                  
                  <div className="border-t border-slate-600 pt-3">
                    <div className="flex justify-between text-lg font-bold">
                      <span className="text-yellow-400">الإجمالي:</span>
                      <span className="text-yellow-400">
                        {formatCurrency(order.pricing.totalPrice, order.pricing.currency)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Field Data Display */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white text-lg flex items-center gap-2">
                      <Edit className="h-5 w-5" />
                      بيانات النموذج
                    </CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowSensitiveData(!showSensitiveData)}
                      className="text-slate-400 hover:text-white"
                    >
                      {showSensitiveData ? (
                        <>
                          <EyeOff className="h-4 w-4 mr-2" />
                          إخفاء البيانات الحساسة
                        </>
                      ) : (
                        <>
                          <Eye className="h-4 w-4 mr-2" />
                          إظهار البيانات الحساسة
                        </>
                      )}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(order.productData).map(([fieldName, fieldValue]) => (
                      <div key={fieldName} className="space-y-1">
                        <Label className="text-slate-400 capitalize">
                          {fieldName.replace(/_/g, ' ')}
                        </Label>
                        <div className="bg-slate-800/50 rounded p-2">
                          <p className="text-white text-sm">
                            {formatFieldValue(fieldName, fieldValue, !showSensitiveData)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Export Data Button */}
                  <div className="mt-4 pt-4 border-t border-slate-600">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const dataStr = JSON.stringify(order.productData, null, 2)
                        const blob = new Blob([dataStr], { type: 'application/json' })
                        const url = URL.createObjectURL(blob)
                        const a = document.createElement('a')
                        a.href = url
                        a.download = `order-${order.id}-data.json`
                        a.click()
                        URL.revokeObjectURL(url)
                      }}
                      className="border-slate-600 text-slate-300"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      تصدير البيانات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Timeline Tab */}
            <TabsContent value="timeline" className="p-4">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    سجل الأحداث
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.timeline.map((event, index) => (
                      <div key={event.id} className="flex gap-4">
                        {/* Timeline indicator */}
                        <div className="flex flex-col items-center">
                          <div className={`w-3 h-3 rounded-full ${
                            event.type === "created" ? "bg-blue-500" :
                            event.type === "status_change" ? "bg-yellow-500" :
                            event.type === "admin_note" ? "bg-purple-500" :
                            "bg-gray-500"
                          }`} />
                          {index < order.timeline.length - 1 && (
                            <div className="w-0.5 h-8 bg-slate-600 mt-2" />
                          )}
                        </div>

                        {/* Event content */}
                        <div className="flex-1 pb-4">
                          <div className="bg-slate-800/50 rounded-lg p-3">
                            <p className="text-white font-medium mb-1">{event.description}</p>
                            <p className="text-slate-400 text-sm">
                              {new Date(event.createdAt).toLocaleString('ar-SA')}
                              {event.createdBy && (
                                <span className="mr-2">• بواسطة: {event.createdBy}</span>
                              )}
                            </p>

                            {/* Event details */}
                            {event.details && Object.keys(event.details).length > 0 && (
                              <div className="mt-2 p-2 bg-slate-700/50 rounded text-sm">
                                {Object.entries(event.details).map(([key, value]) => (
                                  <div key={key} className="text-slate-300">
                                    <strong>{key}:</strong> {String(value)}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Actions Tab */}
            <TabsContent value="actions" className="p-4 space-y-6">
              {/* Quick Actions */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    إجراءات سريعة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {/* Approve Order */}
                    {order.status === "pending" && userRole !== "viewer" && (
                      <Button
                        onClick={() => handleStatusUpdate("processing", "تم قبول الطلب وبدء المعالجة")}
                        disabled={isLoading}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        قبول الطلب
                      </Button>
                    )}

                    {/* Complete Order */}
                    {order.status === "processing" && userRole !== "viewer" && (
                      <Button
                        onClick={() => handleStatusUpdate("completed", "تم إنجاز الطلب بنجاح")}
                        disabled={isLoading}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        إنجاز الطلب
                      </Button>
                    )}

                    {/* Reject Order */}
                    {(order.status === "pending" || order.status === "processing") && userRole !== "viewer" && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="destructive"
                            disabled={isLoading}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            رفض الطلب
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-slate-800 border-slate-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">تأكيد رفض الطلب</AlertDialogTitle>
                            <AlertDialogDescription className="text-slate-400">
                              هل أنت متأكد من رفض هذا الطلب؟ سيتم إشعار العميل برفض الطلب.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                              إلغاء
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleStatusUpdate("failed", "تم رفض الطلب من قبل الإدارة")}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              تأكيد الرفض
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}

                    {/* Delete Order */}
                    {userRole === "admin" && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="destructive"
                            disabled={isLoading}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            حذف الطلب
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-slate-800 border-slate-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">تأكيد حذف الطلب</AlertDialogTitle>
                            <AlertDialogDescription className="text-slate-400">
                              هل أنت متأكد من حذف هذا الطلب؟ هذا الإجراء لا يمكن التراجع عنه.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                              إلغاء
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleDeleteOrder}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              تأكيد الحذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Status Management */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    إدارة الحالة
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-slate-300">تغيير الحالة</Label>
                      <Select
                        value={selectedStatus}
                        onValueChange={(value) => setSelectedStatus(value as OrderStatus)}
                        disabled={userRole === "viewer"}
                      >
                        <SelectTrigger className="bg-slate-600 border-slate-500 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          <SelectItem value="pending">في الانتظار</SelectItem>
                          <SelectItem value="processing">قيد المعالجة</SelectItem>
                          <SelectItem value="completed">مكتمل</SelectItem>
                          <SelectItem value="failed">فشل</SelectItem>
                          <SelectItem value="cancelled">ملغي</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-slate-300">الأولوية</Label>
                      <Select
                        value={selectedPriority}
                        onValueChange={setSelectedPriority}
                        disabled={userRole === "viewer"}
                      >
                        <SelectTrigger className="bg-slate-600 border-slate-500 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          <SelectItem value="low">منخفض</SelectItem>
                          <SelectItem value="normal">عادي</SelectItem>
                          <SelectItem value="high">عالي</SelectItem>
                          <SelectItem value="urgent">عاجل</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {selectedStatus !== order.status && userRole !== "viewer" && (
                    <Button
                      onClick={() => handleStatusUpdate(selectedStatus)}
                      disabled={isLoading}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      تحديث الحالة
                    </Button>
                  )}
                </CardContent>
              </Card>

              {/* Notes Management */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    إدارة الملاحظات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-slate-300">ملاحظات للعميل</Label>
                    <Textarea
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      placeholder="ملاحظات ستكون مرئية للعميل..."
                      className="bg-slate-600 border-slate-500 text-white"
                      rows={3}
                      disabled={userRole === "viewer"}
                    />
                  </div>

                  <div>
                    <Label className="text-slate-300">ملاحظات داخلية</Label>
                    <Textarea
                      value={internalNotes}
                      onChange={(e) => setInternalNotes(e.target.value)}
                      placeholder="ملاحظات داخلية للإدارة فقط..."
                      className="bg-slate-600 border-slate-500 text-white"
                      rows={3}
                      disabled={userRole === "viewer"}
                    />
                  </div>

                  <div>
                    <Label className="text-slate-300">تعيين للمشرف</Label>
                    <Input
                      value={assignedAdmin}
                      onChange={(e) => setAssignedAdmin(e.target.value)}
                      placeholder="معرف المشرف المسؤول..."
                      className="bg-slate-600 border-slate-500 text-white"
                      disabled={userRole === "viewer"}
                    />
                  </div>

                  {userRole !== "viewer" && (
                    <Button
                      onClick={handleSaveUpdates}
                      disabled={isLoading}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      حفظ التحديثات
                    </Button>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
